# 服务器配置
server:
  address: ":8080"
  read_timeout: "30s"
  write_timeout: "30s"
  max_request_body_size: 33554432 # 32MB

# 数据库配置
database:
  dsn: "./data/panel.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

# 安全配置
security:
  jwt_secret: "your-secret-key-change-in-production"
  jwt_expiration: "24h"
  bcrypt_cost: 12

# 日志配置
log:
  level: "info"
  format: "json"
  output: "stdout"

# Docker配置
docker:
  host: "unix:///var/run/docker.sock"
  version: "1.41"
  tls_verify: false
  cert_path: ""
  key_path: ""
  ca_path: ""
  timeout: "30s"
  max_retries: 3

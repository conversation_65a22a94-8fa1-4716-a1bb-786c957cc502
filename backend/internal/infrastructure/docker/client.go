package docker

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
	"github.com/docker/docker/client"

	"panel-backend/internal/config"
)

// Client Docker客户端包装器
type Client struct {
	cli    *client.Client
	config *config.DockerConfig
}

// NewClient 创建新的Docker客户端
func NewClient(cfg *config.DockerConfig) (*Client, error) {
	var httpClient *http.Client

	// 配置TLS
	if cfg.TLSVerify {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: false,
		}

		// 加载证书文件
		if cfg.CertPath != "" && cfg.KeyPath != "" {
			cert, err := tls.LoadX509KeyPair(cfg.CertPath, cfg.KeyPath)
			if err != nil {
				return nil, fmt.Errorf("failed to load TLS certificates: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
		}

		httpClient = &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: tlsConfig,
			},
			Timeout: cfg.Timeout,
		}
	} else {
		httpClient = &http.Client{
			Timeout: cfg.Timeout,
		}
	}

	// 创建Docker客户端
	cli, err := client.NewClientWithOpts(
		client.WithHost(cfg.Host),
		client.WithVersion(cfg.Version),
		client.WithHTTPClient(httpClient),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create docker client: %w", err)
	}

	return &Client{
		cli:    cli,
		config: cfg,
	}, nil
}

// Ping 检查Docker连接
func (c *Client) Ping(ctx context.Context) error {
	_, err := c.cli.Ping(ctx)
	if err != nil {
		return fmt.Errorf("docker ping failed: %w", err)
	}
	return nil
}

// Close 关闭Docker客户端连接
func (c *Client) Close() error {
	return c.cli.Close()
}

// GetInfo 获取Docker系统信息
func (c *Client) GetInfo(ctx context.Context) (interface{}, error) {
	info, err := c.cli.Info(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get docker info: %w", err)
	}
	return info, nil
}

// GetVersion 获取Docker版本信息
func (c *Client) GetVersion(ctx context.Context) (types.Version, error) {
	version, err := c.cli.ServerVersion(ctx)
	if err != nil {
		return types.Version{}, fmt.Errorf("failed to get docker version: %w", err)
	}
	return version, nil
}

// 容器相关方法

// ContainerList 列出容器
func (c *Client) ContainerList(ctx context.Context, options container.ListOptions) ([]types.Container, error) {
	containers, err := c.cli.ContainerList(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("failed to list containers: %w", err)
	}
	return containers, nil
}

// ContainerCreate 创建容器
func (c *Client) ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkingConfig *network.NetworkingConfig, containerName string) (container.CreateResponse, error) {
	resp, err := c.cli.ContainerCreate(ctx, config, hostConfig, networkingConfig, nil, containerName)
	if err != nil {
		return container.CreateResponse{}, fmt.Errorf("failed to create container: %w", err)
	}
	return resp, nil
}

// ContainerStart 启动容器
func (c *Client) ContainerStart(ctx context.Context, containerID string) error {
	err := c.cli.ContainerStart(ctx, containerID, container.StartOptions{})
	if err != nil {
		return fmt.Errorf("failed to start container %s: %w", containerID, err)
	}
	return nil
}

// ContainerStop 停止容器
func (c *Client) ContainerStop(ctx context.Context, containerID string, timeout *int) error {
	var stopTimeout *int
	if timeout != nil {
		stopTimeout = timeout
	}

	err := c.cli.ContainerStop(ctx, containerID, container.StopOptions{Timeout: stopTimeout})
	if err != nil {
		return fmt.Errorf("failed to stop container %s: %w", containerID, err)
	}
	return nil
}

// ContainerRestart 重启容器
func (c *Client) ContainerRestart(ctx context.Context, containerID string, timeout *int) error {
	var restartTimeout *int
	if timeout != nil {
		restartTimeout = timeout
	}

	err := c.cli.ContainerRestart(ctx, containerID, container.StopOptions{Timeout: restartTimeout})
	if err != nil {
		return fmt.Errorf("failed to restart container %s: %w", containerID, err)
	}
	return nil
}

// ContainerRemove 删除容器
func (c *Client) ContainerRemove(ctx context.Context, containerID string, force bool) error {
	err := c.cli.ContainerRemove(ctx, containerID, container.RemoveOptions{Force: force})
	if err != nil {
		return fmt.Errorf("failed to remove container %s: %w", containerID, err)
	}
	return nil
}

// ContainerInspect 检查容器详情
func (c *Client) ContainerInspect(ctx context.Context, containerID string) (types.ContainerJSON, error) {
	containerJSON, err := c.cli.ContainerInspect(ctx, containerID)
	if err != nil {
		return types.ContainerJSON{}, fmt.Errorf("failed to inspect container %s: %w", containerID, err)
	}
	return containerJSON, nil
}

// ContainerLogs 获取容器日志
func (c *Client) ContainerLogs(ctx context.Context, containerID string, options container.LogsOptions) (io.ReadCloser, error) {
	logs, err := c.cli.ContainerLogs(ctx, containerID, options)
	if err != nil {
		return nil, fmt.Errorf("failed to get container logs %s: %w", containerID, err)
	}
	return logs, nil
}

// ContainerStats 获取容器统计信息
func (c *Client) ContainerStats(ctx context.Context, containerID string, stream bool) (interface{}, error) {
	stats, err := c.cli.ContainerStats(ctx, containerID, stream)
	if err != nil {
		return nil, fmt.Errorf("failed to get container stats %s: %w", containerID, err)
	}
	return stats, nil
}

// 镜像相关方法

// ImageList 列出镜像
func (c *Client) ImageList(ctx context.Context, options image.ListOptions) ([]image.Summary, error) {
	images, err := c.cli.ImageList(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("failed to list images: %w", err)
	}
	return images, nil
}

// ImagePull 拉取镜像
func (c *Client) ImagePull(ctx context.Context, refStr string, options image.PullOptions) (io.ReadCloser, error) {
	resp, err := c.cli.ImagePull(ctx, refStr, options)
	if err != nil {
		return nil, fmt.Errorf("failed to pull image %s: %w", refStr, err)
	}
	return resp, nil
}

// ImageRemove 删除镜像
func (c *Client) ImageRemove(ctx context.Context, imageID string, options image.RemoveOptions) ([]image.DeleteResponse, error) {
	resp, err := c.cli.ImageRemove(ctx, imageID, options)
	if err != nil {
		return nil, fmt.Errorf("failed to remove image %s: %w", imageID, err)
	}
	return resp, nil
}

// ImageInspect 检查镜像详情
func (c *Client) ImageInspect(ctx context.Context, imageID string) (types.ImageInspect, error) {
	imageInspect, _, err := c.cli.ImageInspectWithRaw(ctx, imageID)
	if err != nil {
		return types.ImageInspect{}, fmt.Errorf("failed to inspect image %s: %w", imageID, err)
	}
	return imageInspect, nil
}

// 网络相关方法

// NetworkList 列出网络
func (c *Client) NetworkList(ctx context.Context, options network.ListOptions) ([]network.Summary, error) {
	networks, err := c.cli.NetworkList(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("failed to list networks: %w", err)
	}
	return networks, nil
}

// NetworkCreate 创建网络
func (c *Client) NetworkCreate(ctx context.Context, name string, options network.CreateOptions) (network.CreateResponse, error) {
	resp, err := c.cli.NetworkCreate(ctx, name, options)
	if err != nil {
		return network.CreateResponse{}, fmt.Errorf("failed to create network %s: %w", name, err)
	}
	return resp, nil
}

// NetworkRemove 删除网络
func (c *Client) NetworkRemove(ctx context.Context, networkID string) error {
	err := c.cli.NetworkRemove(ctx, networkID)
	if err != nil {
		return fmt.Errorf("failed to remove network %s: %w", networkID, err)
	}
	return nil
}

// NetworkInspect 检查网络详情
func (c *Client) NetworkInspect(ctx context.Context, networkID string, options network.InspectOptions) (network.Inspect, error) {
	networkInspect, err := c.cli.NetworkInspect(ctx, networkID, options)
	if err != nil {
		return network.Inspect{}, fmt.Errorf("failed to inspect network %s: %w", networkID, err)
	}
	return networkInspect, nil
}

// 卷相关方法

// VolumeList 列出卷
func (c *Client) VolumeList(ctx context.Context, options volume.ListOptions) (volume.ListResponse, error) {
	volumes, err := c.cli.VolumeList(ctx, options)
	if err != nil {
		return volume.ListResponse{}, fmt.Errorf("failed to list volumes: %w", err)
	}
	return volumes, nil
}

// VolumeCreate 创建卷
func (c *Client) VolumeCreate(ctx context.Context, options volume.CreateOptions) (volume.Volume, error) {
	vol, err := c.cli.VolumeCreate(ctx, options)
	if err != nil {
		return volume.Volume{}, fmt.Errorf("failed to create volume: %w", err)
	}
	return vol, nil
}

// VolumeRemove 删除卷
func (c *Client) VolumeRemove(ctx context.Context, volumeID string, force bool) error {
	err := c.cli.VolumeRemove(ctx, volumeID, force)
	if err != nil {
		return fmt.Errorf("failed to remove volume %s: %w", volumeID, err)
	}
	return nil
}

// VolumeInspect 检查卷详情
func (c *Client) VolumeInspect(ctx context.Context, volumeID string) (volume.Volume, error) {
	vol, err := c.cli.VolumeInspect(ctx, volumeID)
	if err != nil {
		return volume.Volume{}, fmt.Errorf("failed to inspect volume %s: %w", volumeID, err)
	}
	return vol, nil
}

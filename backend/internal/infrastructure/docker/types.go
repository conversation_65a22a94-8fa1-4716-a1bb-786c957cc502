package docker

import (
	"time"
)

// ContainerStatus 容器状态枚举
type ContainerStatus string

const (
	ContainerStatusCreated    ContainerStatus = "created"
	ContainerStatusRestarting ContainerStatus = "restarting"
	ContainerStatusRunning    ContainerStatus = "running"
	ContainerStatusRemoving   ContainerStatus = "removing"
	ContainerStatusPaused     ContainerStatus = "paused"
	ContainerStatusExited     ContainerStatus = "exited"
	ContainerStatusDead       ContainerStatus = "dead"
)

// ImageStatus 镜像状态枚举
type ImageStatus string

const (
	ImageStatusAvailable ImageStatus = "available"
	ImageStatusPulling   ImageStatus = "pulling"
	ImageStatusBuilding  ImageStatus = "building"
	ImageStatusError     ImageStatus = "error"
)

// NetworkDriver 网络驱动类型
type NetworkDriver string

const (
	NetworkDriverBridge NetworkDriver = "bridge"
	NetworkDriverHost   NetworkDriver = "host"
	NetworkDriverNone   NetworkDriver = "none"
	NetworkDriverOverlay NetworkDriver = "overlay"
	NetworkDriverMacvlan NetworkDriver = "macvlan"
)

// VolumeDriver 卷驱动类型
type VolumeDriver string

const (
	VolumeDriverLocal VolumeDriver = "local"
	VolumeDriverNFS   VolumeDriver = "nfs"
)

// ContainerCreateRequest 容器创建请求
type ContainerCreateRequest struct {
	Name        string            `json:"name" validate:"required"`
	Image       string            `json:"image" validate:"required"`
	Command     []string          `json:"command,omitempty"`
	Entrypoint  []string          `json:"entrypoint,omitempty"`
	Environment []string          `json:"environment,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Ports       []PortMapping     `json:"ports,omitempty"`
	Volumes     []VolumeMount     `json:"volumes,omitempty"`
	Networks    []string          `json:"networks,omitempty"`
	RestartPolicy RestartPolicy   `json:"restart_policy,omitempty"`
	Resources   ResourceLimits    `json:"resources,omitempty"`
	HostID      uint              `json:"host_id"`
}

// PortMapping 端口映射
type PortMapping struct {
	HostPort      string `json:"host_port"`
	ContainerPort string `json:"container_port"`
	Protocol      string `json:"protocol"` // tcp, udp
	HostIP        string `json:"host_ip,omitempty"`
}

// VolumeMount 卷挂载
type VolumeMount struct {
	Source      string `json:"source"`      // 主机路径或卷名
	Target      string `json:"target"`      // 容器内路径
	Type        string `json:"type"`        // bind, volume, tmpfs
	ReadOnly    bool   `json:"read_only"`
	Consistency string `json:"consistency,omitempty"` // default, consistent, cached, delegated
}

// RestartPolicy 重启策略
type RestartPolicy struct {
	Name              string `json:"name"`                // no, always, on-failure, unless-stopped
	MaximumRetryCount int    `json:"maximum_retry_count"` // 仅对on-failure有效
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	Memory     int64   `json:"memory"`      // 内存限制（字节）
	MemorySwap int64   `json:"memory_swap"` // 内存+交换限制（字节）
	CPUShares  int64   `json:"cpu_shares"`  // CPU权重
	CPUQuota   int64   `json:"cpu_quota"`   // CPU配额
	CPUPeriod  int64   `json:"cpu_period"`  // CPU周期
	CPUCount   float64 `json:"cpu_count"`   // CPU核心数
}

// ContainerInfo 容器信息
type ContainerInfo struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Image       string            `json:"image"`
	ImageID     string            `json:"image_id"`
	Status      ContainerStatus   `json:"status"`
	State       string            `json:"state"`
	Created     time.Time         `json:"created"`
	Started     *time.Time        `json:"started,omitempty"`
	Finished    *time.Time        `json:"finished,omitempty"`
	ExitCode    *int              `json:"exit_code,omitempty"`
	Ports       []PortMapping     `json:"ports"`
	Volumes     []VolumeMount     `json:"volumes"`
	Networks    []NetworkInfo     `json:"networks"`
	Labels      map[string]string `json:"labels"`
	Environment []string          `json:"environment"`
	Command     []string          `json:"command"`
	Entrypoint  []string          `json:"entrypoint"`
	WorkingDir  string            `json:"working_dir"`
	User        string            `json:"user"`
	Hostname    string            `json:"hostname"`
	Domainname  string            `json:"domainname"`
	Resources   ResourceUsage     `json:"resources"`
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Driver      NetworkDriver     `json:"driver"`
	Scope       string            `json:"scope"`
	Internal    bool              `json:"internal"`
	Attachable  bool              `json:"attachable"`
	Ingress     bool              `json:"ingress"`
	IPAMConfig  []IPAMConfig      `json:"ipam_config"`
	Options     map[string]string `json:"options"`
	Labels      map[string]string `json:"labels"`
	Created     time.Time         `json:"created"`
	Containers  []string          `json:"containers"` // 连接的容器ID列表
}

// IPAMConfig IP地址管理配置
type IPAMConfig struct {
	Subnet  string `json:"subnet"`
	Gateway string `json:"gateway"`
	IPRange string `json:"ip_range,omitempty"`
}

// VolumeInfo 卷信息
type VolumeInfo struct {
	ID         string            `json:"id"`
	Name       string            `json:"name"`
	Driver     VolumeDriver      `json:"driver"`
	Mountpoint string            `json:"mountpoint"`
	Scope      string            `json:"scope"`
	Options    map[string]string `json:"options"`
	Labels     map[string]string `json:"labels"`
	Created    time.Time         `json:"created"`
	UsageData  *VolumeUsage      `json:"usage_data,omitempty"`
}

// VolumeUsage 卷使用情况
type VolumeUsage struct {
	Size         int64 `json:"size"`          // 卷大小（字节）
	RefCount     int64 `json:"ref_count"`     // 引用计数
	UsedSize     int64 `json:"used_size"`     // 已使用大小（字节）
	AvailableSize int64 `json:"available_size"` // 可用大小（字节）
}

// ImageInfo 镜像信息
type ImageInfo struct {
	ID          string            `json:"id"`
	RepoTags    []string          `json:"repo_tags"`
	RepoDigests []string          `json:"repo_digests"`
	Parent      string            `json:"parent"`
	Comment     string            `json:"comment"`
	Created     time.Time         `json:"created"`
	Size        int64             `json:"size"`
	VirtualSize int64             `json:"virtual_size"`
	SharedSize  int64             `json:"shared_size"`
	Labels      map[string]string `json:"labels"`
	Config      ImageConfig       `json:"config"`
	Architecture string           `json:"architecture"`
	OS          string            `json:"os"`
	Status      ImageStatus       `json:"status"`
}

// ImageConfig 镜像配置
type ImageConfig struct {
	User         string            `json:"user"`
	ExposedPorts map[string]struct{} `json:"exposed_ports"`
	Environment  []string          `json:"environment"`
	Entrypoint   []string          `json:"entrypoint"`
	Command      []string          `json:"command"`
	Volumes      map[string]struct{} `json:"volumes"`
	WorkingDir   string            `json:"working_dir"`
	Labels       map[string]string `json:"labels"`
	StopSignal   string            `json:"stop_signal"`
	StopTimeout  *int              `json:"stop_timeout"`
	Shell        []string          `json:"shell"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUUsage    CPUUsage    `json:"cpu_usage"`
	MemoryUsage MemoryUsage `json:"memory_usage"`
	NetworkIO   NetworkIO   `json:"network_io"`
	BlockIO     BlockIO     `json:"block_io"`
	PIDs        PIDsUsage   `json:"pids"`
}

// CPUUsage CPU使用情况
type CPUUsage struct {
	TotalUsage        uint64  `json:"total_usage"`         // 总CPU使用时间（纳秒）
	UsageInKernelmode uint64  `json:"usage_in_kernelmode"` // 内核态CPU使用时间（纳秒）
	UsageInUsermode   uint64  `json:"usage_in_usermode"`   // 用户态CPU使用时间（纳秒）
	SystemCPUUsage    uint64  `json:"system_cpu_usage"`    // 系统CPU使用时间（纳秒）
	OnlineCPUs        uint32  `json:"online_cpus"`         // 在线CPU数量
	ThrottlingData    Throttling `json:"throttling_data"`  // CPU限流数据
	CPUPercent        float64 `json:"cpu_percent"`         // CPU使用百分比
}

// Throttling CPU限流数据
type Throttling struct {
	Periods          uint64 `json:"periods"`           // 限流周期数
	ThrottledPeriods uint64 `json:"throttled_periods"` // 被限流的周期数
	ThrottledTime    uint64 `json:"throttled_time"`    // 被限流的时间（纳秒）
}

// MemoryUsage 内存使用情况
type MemoryUsage struct {
	Usage    uint64  `json:"usage"`     // 当前内存使用量（字节）
	MaxUsage uint64  `json:"max_usage"` // 最大内存使用量（字节）
	Limit    uint64  `json:"limit"`     // 内存限制（字节）
	Percent  float64 `json:"percent"`   // 内存使用百分比
	Stats    MemoryStats `json:"stats"` // 详细内存统计
}

// MemoryStats 详细内存统计
type MemoryStats struct {
	Cache             uint64 `json:"cache"`              // 缓存内存（字节）
	RSS               uint64 `json:"rss"`                // 常驻内存（字节）
	RSSHuge           uint64 `json:"rss_huge"`           // 大页内存（字节）
	MappedFile        uint64 `json:"mapped_file"`        // 映射文件内存（字节）
	Dirty             uint64 `json:"dirty"`              // 脏页内存（字节）
	Writeback         uint64 `json:"writeback"`          // 回写内存（字节）
	PgpgIn            uint64 `json:"pgpgin"`             // 页面换入次数
	PgpgOut           uint64 `json:"pgpgout"`            // 页面换出次数
	PgFault           uint64 `json:"pgfault"`            // 页面错误次数
	PgMajFault        uint64 `json:"pgmajfault"`         // 主要页面错误次数
	InactiveAnon      uint64 `json:"inactive_anon"`      // 非活跃匿名内存（字节）
	ActiveAnon        uint64 `json:"active_anon"`        // 活跃匿名内存（字节）
	InactiveFile      uint64 `json:"inactive_file"`      // 非活跃文件内存（字节）
	ActiveFile        uint64 `json:"active_file"`        // 活跃文件内存（字节）
	Unevictable       uint64 `json:"unevictable"`        // 不可回收内存（字节）
}

// NetworkIO 网络IO统计
type NetworkIO struct {
	RxBytes   uint64 `json:"rx_bytes"`   // 接收字节数
	RxPackets uint64 `json:"rx_packets"` // 接收包数
	RxErrors  uint64 `json:"rx_errors"`  // 接收错误数
	RxDropped uint64 `json:"rx_dropped"` // 接收丢弃数
	TxBytes   uint64 `json:"tx_bytes"`   // 发送字节数
	TxPackets uint64 `json:"tx_packets"` // 发送包数
	TxErrors  uint64 `json:"tx_errors"`  // 发送错误数
	TxDropped uint64 `json:"tx_dropped"` // 发送丢弃数
}

// BlockIO 块设备IO统计
type BlockIO struct {
	ReadBytes  uint64 `json:"read_bytes"`  // 读取字节数
	WriteBytes uint64 `json:"write_bytes"` // 写入字节数
	ReadOps    uint64 `json:"read_ops"`    // 读取操作数
	WriteOps   uint64 `json:"write_ops"`   // 写入操作数
}

// PIDsUsage 进程ID使用情况
type PIDsUsage struct {
	Current uint64 `json:"current"` // 当前进程数
	Limit   uint64 `json:"limit"`   // 进程数限制
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Stream    string    `json:"stream"` // stdout, stderr
	Log       string    `json:"log"`
}

// EventType 事件类型
type EventType string

const (
	EventTypeContainer EventType = "container"
	EventTypeImage     EventType = "image"
	EventTypeNetwork   EventType = "network"
	EventTypeVolume    EventType = "volume"
	EventTypePlugin    EventType = "plugin"
	EventTypeNode      EventType = "node"
	EventTypeService   EventType = "service"
	EventTypeSecret    EventType = "secret"
	EventTypeConfig    EventType = "config"
)

// EventAction 事件动作
type EventAction string

const (
	EventActionCreate  EventAction = "create"
	EventActionStart   EventAction = "start"
	EventActionStop    EventAction = "stop"
	EventActionRestart EventAction = "restart"
	EventActionKill    EventAction = "kill"
	EventActionDie     EventAction = "die"
	EventActionDestroy EventAction = "destroy"
	EventActionPull    EventAction = "pull"
	EventActionPush    EventAction = "push"
	EventActionTag     EventAction = "tag"
	EventActionUntag   EventAction = "untag"
	EventActionImport  EventAction = "import"
	EventActionExport  EventAction = "export"
	EventActionSave    EventAction = "save"
	EventActionLoad    EventAction = "load"
	EventActionMount   EventAction = "mount"
	EventActionUnmount EventAction = "unmount"
)

// Event Docker事件
type Event struct {
	Type     EventType         `json:"type"`
	Action   EventAction       `json:"action"`
	Actor    EventActor        `json:"actor"`
	Time     time.Time         `json:"time"`
	TimeNano int64             `json:"time_nano"`
	Scope    string            `json:"scope"`
}

// EventActor 事件执行者
type EventActor struct {
	ID         string            `json:"id"`
	Attributes map[string]string `json:"attributes"`
}

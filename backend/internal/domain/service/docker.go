package service

import (
	"context"
	"io"

	dockerTypes "panel-backend/internal/infrastructure/docker"
)

// DockerService Docker服务接口
type DockerService interface {
	// 系统信息
	Ping(ctx context.Context) error
	GetInfo(ctx context.Context) (interface{}, error)
	GetVersion(ctx context.Context) (interface{}, error)

	// 容器管理
	ListContainers(ctx context.Context, all bool) ([]dockerTypes.ContainerInfo, error)
	GetContainer(ctx context.Context, containerID string) (*dockerTypes.ContainerInfo, error)
	CreateContainer(ctx context.Context, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error)
	StartContainer(ctx context.Context, containerID string) error
	StopContainer(ctx context.Context, containerID string, timeout *int) error
	RestartContainer(ctx context.Context, containerID string, timeout *int) error
	RemoveContainer(ctx context.Context, containerID string, force bool) error
	GetContainerLogs(ctx context.Context, containerID string, follow bool, tail string) ([]dockerTypes.LogEntry, error)
	GetContainerStats(ctx context.Context, containerID string) (*dockerTypes.ResourceUsage, error)

	// 镜像管理
	ListImages(ctx context.Context) ([]dockerTypes.ImageInfo, error)
	GetImage(ctx context.Context, imageID string) (*dockerTypes.ImageInfo, error)
	PullImage(ctx context.Context, imageName string, tag string) error
	RemoveImage(ctx context.Context, imageID string, force bool) error

	// 网络管理
	ListNetworks(ctx context.Context) ([]dockerTypes.NetworkInfo, error)
	GetNetwork(ctx context.Context, networkID string) (*dockerTypes.NetworkInfo, error)
	CreateNetwork(ctx context.Context, name string, driver dockerTypes.NetworkDriver, options map[string]string) (*dockerTypes.NetworkInfo, error)
	RemoveNetwork(ctx context.Context, networkID string) error

	// 卷管理
	ListVolumes(ctx context.Context) ([]dockerTypes.VolumeInfo, error)
	GetVolume(ctx context.Context, volumeID string) (*dockerTypes.VolumeInfo, error)
	CreateVolume(ctx context.Context, name string, driver dockerTypes.VolumeDriver, options map[string]string) (*dockerTypes.VolumeInfo, error)
	RemoveVolume(ctx context.Context, volumeID string, force bool) error

	// 事件监听
	ListenEvents(ctx context.Context, eventChan chan<- dockerTypes.Event) error
}

// ContainerService 容器服务接口
type ContainerService interface {
	// 基础CRUD操作
	List(ctx context.Context, hostID uint, status string, search string, page, size int) ([]dockerTypes.ContainerInfo, int64, error)
	Get(ctx context.Context, id uint) (*dockerTypes.ContainerInfo, error)
	Create(ctx context.Context, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error)
	Update(ctx context.Context, id uint, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error)
	Delete(ctx context.Context, id uint, force bool) error

	// 生命周期管理
	Start(ctx context.Context, id uint) error
	Stop(ctx context.Context, id uint, timeout *int) error
	Restart(ctx context.Context, id uint, timeout *int) error

	// 监控和日志
	GetLogs(ctx context.Context, id uint, follow bool, tail string) ([]dockerTypes.LogEntry, error)
	GetStats(ctx context.Context, id uint) (*dockerTypes.ResourceUsage, error)

	// 状态同步
	SyncStatus(ctx context.Context, hostID uint) error
	SyncAllStatus(ctx context.Context) error
}

// ImageService 镜像服务接口
type ImageService interface {
	// 基础CRUD操作
	List(ctx context.Context, hostID uint, search string, page, size int) ([]dockerTypes.ImageInfo, int64, error)
	Get(ctx context.Context, id uint) (*dockerTypes.ImageInfo, error)
	Pull(ctx context.Context, hostID uint, repository, tag string) (*dockerTypes.ImageInfo, error)
	Delete(ctx context.Context, id uint, force bool) error

	// 镜像操作
	GetHistory(ctx context.Context, imageID string) ([]interface{}, error)
	Export(ctx context.Context, imageID string) (io.ReadCloser, error)
	Import(ctx context.Context, source io.Reader, repository, tag string) (*dockerTypes.ImageInfo, error)

	// 状态同步
	SyncImages(ctx context.Context, hostID uint) error
	SyncAllImages(ctx context.Context) error
}

// NetworkService 网络服务接口
type NetworkService interface {
	// 基础CRUD操作
	List(ctx context.Context, hostID uint, search string) ([]dockerTypes.NetworkInfo, error)
	Get(ctx context.Context, id uint) (*dockerTypes.NetworkInfo, error)
	Create(ctx context.Context, hostID uint, name string, driver dockerTypes.NetworkDriver, options map[string]string) (*dockerTypes.NetworkInfo, error)
	Delete(ctx context.Context, id uint) error

	// 网络操作
	Connect(ctx context.Context, networkID, containerID string) error
	Disconnect(ctx context.Context, networkID, containerID string, force bool) error
	Prune(ctx context.Context, hostID uint) error

	// 状态同步
	SyncNetworks(ctx context.Context, hostID uint) error
	SyncAllNetworks(ctx context.Context) error
}

// VolumeService 卷服务接口
type VolumeService interface {
	// 基础CRUD操作
	List(ctx context.Context, hostID uint, search string) ([]dockerTypes.VolumeInfo, error)
	Get(ctx context.Context, id uint) (*dockerTypes.VolumeInfo, error)
	Create(ctx context.Context, hostID uint, name string, driver dockerTypes.VolumeDriver, options map[string]string) (*dockerTypes.VolumeInfo, error)
	Delete(ctx context.Context, id uint, force bool) error

	// 卷操作
	Prune(ctx context.Context, hostID uint) error
	GetUsage(ctx context.Context, volumeID string) (*dockerTypes.VolumeUsage, error)

	// 状态同步
	SyncVolumes(ctx context.Context, hostID uint) error
	SyncAllVolumes(ctx context.Context) error
}

// MonitorService 监控服务接口
type MonitorService interface {
	// 系统监控
	GetSystemInfo(ctx context.Context, hostID uint) (interface{}, error)
	GetSystemStats(ctx context.Context, hostID uint) (interface{}, error)

	// 容器监控
	GetContainerStats(ctx context.Context, containerID string) (*dockerTypes.ResourceUsage, error)
	GetContainerStatsStream(ctx context.Context, containerID string, statsChan chan<- *dockerTypes.ResourceUsage) error
	GetAllContainerStats(ctx context.Context, hostID uint) (map[string]*dockerTypes.ResourceUsage, error)

	// 事件监控
	StartEventMonitor(ctx context.Context, hostID uint) error
	StopEventMonitor(ctx context.Context, hostID uint) error
	GetEvents(ctx context.Context, hostID uint, since, until string, eventType dockerTypes.EventType) ([]dockerTypes.Event, error)

	// 健康检查
	HealthCheck(ctx context.Context, hostID uint) error
	GetHealthStatus(ctx context.Context, hostID uint) (interface{}, error)
}

// LogService 日志服务接口
type LogService interface {
	// 容器日志
	GetContainerLogs(ctx context.Context, containerID string, options LogOptions) ([]dockerTypes.LogEntry, error)
	StreamContainerLogs(ctx context.Context, containerID string, options LogOptions, logChan chan<- dockerTypes.LogEntry) error

	// 系统日志
	GetSystemLogs(ctx context.Context, hostID uint, options LogOptions) ([]interface{}, error)
	
	// 日志管理
	RotateLogs(ctx context.Context, containerID string) error
	CleanupLogs(ctx context.Context, hostID uint, olderThan string) error
}

// LogOptions 日志选项
type LogOptions struct {
	Follow     bool   `json:"follow"`      // 是否跟踪日志
	Tail       string `json:"tail"`        // 显示最后N行，"all"表示全部
	Since      string `json:"since"`       // 开始时间
	Until      string `json:"until"`       // 结束时间
	Timestamps bool   `json:"timestamps"`  // 是否显示时间戳
	Details    bool   `json:"details"`     // 是否显示详细信息
}

// TaskService 任务服务接口
type TaskService interface {
	// 异步任务管理
	CreateTask(ctx context.Context, taskType, name string, params interface{}) (uint, error)
	GetTask(ctx context.Context, taskID uint) (interface{}, error)
	ListTasks(ctx context.Context, userID uint, status string, page, size int) ([]interface{}, int64, error)
	CancelTask(ctx context.Context, taskID uint) error

	// 任务执行
	ExecutePullImageTask(ctx context.Context, taskID uint, hostID uint, repository, tag string) error
	ExecuteCreateContainerTask(ctx context.Context, taskID uint, req *dockerTypes.ContainerCreateRequest) error
	ExecuteBackupTask(ctx context.Context, taskID uint, containerID string, backupPath string) error
	ExecuteRestoreTask(ctx context.Context, taskID uint, containerID string, backupPath string) error

	// 任务调度
	ScheduleTask(ctx context.Context, taskID uint, scheduleTime string) error
	GetScheduledTasks(ctx context.Context) ([]interface{}, error)
}

// BackupService 备份服务接口
type BackupService interface {
	// 容器备份
	BackupContainer(ctx context.Context, containerID string, backupPath string) error
	RestoreContainer(ctx context.Context, backupPath string, newName string) (*dockerTypes.ContainerInfo, error)
	ListBackups(ctx context.Context, containerID string) ([]interface{}, error)
	DeleteBackup(ctx context.Context, backupID string) error

	// 镜像备份
	ExportImage(ctx context.Context, imageID string, exportPath string) error
	ImportImage(ctx context.Context, importPath string, repository, tag string) (*dockerTypes.ImageInfo, error)

	// 卷备份
	BackupVolume(ctx context.Context, volumeID string, backupPath string) error
	RestoreVolume(ctx context.Context, backupPath string, newName string) (*dockerTypes.VolumeInfo, error)

	// 备份管理
	ScheduleBackup(ctx context.Context, resourceType, resourceID string, schedule string) error
	GetBackupSchedules(ctx context.Context) ([]interface{}, error)
	CleanupOldBackups(ctx context.Context, olderThan string) error
}

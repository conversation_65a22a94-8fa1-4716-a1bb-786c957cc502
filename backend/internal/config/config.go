package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Security SecurityConfig `mapstructure:"security"`
	Log      LogConfig      `mapstructure:"log"`
	Docker   DockerConfig   `mapstructure:"docker"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Address            string        `mapstructure:"address"`
	ReadTimeout        time.Duration `mapstructure:"read_timeout"`
	WriteTimeout       time.Duration `mapstructure:"write_timeout"`
	MaxRequestBodySize int           `mapstructure:"max_request_body_size"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	DSN             string        `mapstructure:"dsn"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	JWTSecret     string        `mapstructure:"jwt_secret"`
	JWTExpiration time.Duration `mapstructure:"jwt_expiration"`
	BCryptCost    int           `mapstructure:"bcrypt_cost"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// DockerConfig Docker配置
type DockerConfig struct {
	Host       string        `mapstructure:"host"`
	Version    string        `mapstructure:"version"`
	TLSVerify  bool          `mapstructure:"tls_verify"`
	CertPath   string        `mapstructure:"cert_path"`
	KeyPath    string        `mapstructure:"key_path"`
	CAPath     string        `mapstructure:"ca_path"`
	Timeout    time.Duration `mapstructure:"timeout"`
	MaxRetries int           `mapstructure:"max_retries"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")

	// 设置默认值
	setDefaults()

	// 读取环境变量
	viper.AutomaticEnv()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		// 如果配置文件不存在，使用默认配置
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.address", ":8080")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.max_request_body_size", 32*1024*1024) // 32MB

	// 数据库默认配置
	viper.SetDefault("database.dsn", "./data/panel.db")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "5m")

	// 安全默认配置
	viper.SetDefault("security.jwt_secret", "your-secret-key-change-in-production")
	viper.SetDefault("security.jwt_expiration", "24h")
	viper.SetDefault("security.bcrypt_cost", 12)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// Docker默认配置
	viper.SetDefault("docker.host", "unix:///var/run/docker.sock")
	viper.SetDefault("docker.version", "1.41")
	viper.SetDefault("docker.tls_verify", false)
	viper.SetDefault("docker.cert_path", "")
	viper.SetDefault("docker.key_path", "")
	viper.SetDefault("docker.ca_path", "")
	viper.SetDefault("docker.timeout", "30s")
	viper.SetDefault("docker.max_retries", 3)
}

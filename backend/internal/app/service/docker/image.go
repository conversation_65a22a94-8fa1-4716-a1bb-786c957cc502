package docker

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/docker/docker/api/types/image"
	"gorm.io/gorm"

	"panel-backend/internal/domain/entity"
	"panel-backend/internal/domain/service"
	dockerClient "panel-backend/internal/infrastructure/docker"
	dockerTypes "panel-backend/internal/infrastructure/docker"
)

// imageService 镜像服务实现
type imageService struct {
	db           *gorm.DB
	dockerClient *dockerClient.Client
}

// NewImageService 创建镜像服务
func NewImageService(db *gorm.DB, dockerClient *dockerClient.Client) service.ImageService {
	return &imageService{
		db:           db,
		dockerClient: dockerClient,
	}
}

// List 获取镜像列表
func (s *imageService) List(ctx context.Context, hostID uint, search string, page, size int) ([]dockerTypes.ImageInfo, int64, error) {
	// 构建数据库查询
	query := s.db.Model(&entity.Image{})
	
	if hostID > 0 {
		query = query.Where("host_id = ?", hostID)
	}
	
	if search != "" {
		query = query.Where("repository LIKE ? OR tag LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count images: %w", err)
	}

	// 分页查询
	var images []entity.Image
	offset := (page - 1) * size
	if err := query.Preload("Host").Offset(offset).Limit(size).Find(&images).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list images: %w", err)
	}

	// 转换为Docker类型
	result := make([]dockerTypes.ImageInfo, len(images))
	for i, img := range images {
		imageInfo, err := s.convertToDockerImageInfo(&img)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to convert image %s: %w", img.Repository, err)
		}
		result[i] = *imageInfo
	}

	return result, total, nil
}

// Get 获取镜像详情
func (s *imageService) Get(ctx context.Context, id uint) (*dockerTypes.ImageInfo, error) {
	var img entity.Image
	if err := s.db.Preload("Host").First(&img, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("image not found")
		}
		return nil, fmt.Errorf("failed to get image: %w", err)
	}

	// 从Docker API获取实时信息
	if img.ImageID != "" {
		dockerImage, err := s.dockerClient.ImageInspect(ctx, img.ImageID)
		if err == nil {
			// 更新数据库中的信息
			if dockerImage.Size > 0 {
				s.db.Model(&img).Update("size", dockerImage.Size)
				img.Size = dockerImage.Size
			}
		}
	}

	return s.convertToDockerImageInfo(&img)
}

// Pull 拉取镜像
func (s *imageService) Pull(ctx context.Context, hostID uint, repository, tag string) (*dockerTypes.ImageInfo, error) {
	if tag == "" {
		tag = "latest"
	}

	imageRef := repository + ":" + tag

	// 拉取镜像
	pullResp, err := s.dockerClient.ImagePull(ctx, imageRef, image.PullOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to pull image %s: %w", imageRef, err)
	}
	defer pullResp.Close()

	// 读取拉取响应（这里简化处理，实际应该解析进度信息）
	_, err = io.ReadAll(pullResp)
	if err != nil {
		return nil, fmt.Errorf("failed to read pull response: %w", err)
	}

	// 获取拉取后的镜像信息
	dockerImage, err := s.dockerClient.ImageInspect(ctx, imageRef)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect pulled image: %w", err)
	}

	// 保存到数据库
	img := entity.Image{
		ImageID:     dockerImage.ID,
		Repository:  repository,
		Tag:         tag,
		Size:        dockerImage.Size,
		CreatedTime: time.Unix(dockerImage.Created.Unix(), 0),
		HostID:      hostID,
	}

	if err := s.db.Create(&img).Error; err != nil {
		return nil, fmt.Errorf("failed to save image to database: %w", err)
	}

	return s.convertToDockerImageInfo(&img)
}

// Delete 删除镜像
func (s *imageService) Delete(ctx context.Context, id uint, force bool) error {
	var img entity.Image
	if err := s.db.First(&img, id).Error; err != nil {
		return fmt.Errorf("image not found: %w", err)
	}

	// 从Docker删除镜像
	if img.ImageID != "" {
		_, err := s.dockerClient.ImageRemove(ctx, img.ImageID, image.RemoveOptions{Force: force})
		if err != nil {
			return fmt.Errorf("failed to remove docker image: %w", err)
		}
	}

	// 从数据库删除
	if err := s.db.Delete(&img).Error; err != nil {
		return fmt.Errorf("failed to delete image from database: %w", err)
	}

	return nil
}

// GetHistory 获取镜像历史
func (s *imageService) GetHistory(ctx context.Context, imageID string) ([]interface{}, error) {
	// 这里简化处理，返回空历史
	// 实际实现需要调用Docker API获取镜像历史
	return []interface{}{}, nil
}

// Export 导出镜像
func (s *imageService) Export(ctx context.Context, imageID string) (io.ReadCloser, error) {
	// 这里简化处理，实际需要实现镜像导出
	return nil, fmt.Errorf("export not implemented yet")
}

// Import 导入镜像
func (s *imageService) Import(ctx context.Context, source io.Reader, repository, tag string) (*dockerTypes.ImageInfo, error) {
	// 这里简化处理，实际需要实现镜像导入
	return nil, fmt.Errorf("import not implemented yet")
}

// SyncImages 同步镜像
func (s *imageService) SyncImages(ctx context.Context, hostID uint) error {
	// 获取Docker中的所有镜像
	dockerImages, err := s.dockerClient.ImageList(ctx, image.ListOptions{})
	if err != nil {
		return fmt.Errorf("failed to list docker images: %w", err)
	}

	// 获取数据库中该主机的所有镜像
	var dbImages []entity.Image
	if err := s.db.Where("host_id = ?", hostID).Find(&dbImages).Error; err != nil {
		return fmt.Errorf("failed to get database images: %w", err)
	}

	// 创建数据库镜像ID映射
	dbImageMap := make(map[string]*entity.Image)
	for i := range dbImages {
		dbImageMap[dbImages[i].ImageID] = &dbImages[i]
	}

	// 同步Docker镜像到数据库
	for _, dockerImage := range dockerImages {
		if len(dockerImage.RepoTags) == 0 {
			continue // 跳过没有标签的镜像
		}

		// 解析仓库和标签
		repoTag := dockerImage.RepoTags[0]
		parts := strings.Split(repoTag, ":")
		repository := parts[0]
		tag := "latest"
		if len(parts) > 1 {
			tag = parts[1]
		}

		if dbImage, exists := dbImageMap[dockerImage.ID]; exists {
			// 更新现有镜像
			dbImage.Size = dockerImage.Size
			dbImage.Repository = repository
			dbImage.Tag = tag
			s.db.Save(dbImage)
		} else {
			// 创建新镜像记录
			newImage := entity.Image{
				ImageID:     dockerImage.ID,
				Repository:  repository,
				Tag:         tag,
				Size:        dockerImage.Size,
				CreatedTime: time.Unix(dockerImage.Created, 0),
				HostID:      hostID,
			}
			s.db.Create(&newImage)
		}
	}

	// 标记数据库中不存在于Docker的镜像
	dockerImageIDs := make(map[string]bool)
	for _, dockerImage := range dockerImages {
		dockerImageIDs[dockerImage.ID] = true
	}

	for _, dbImage := range dbImages {
		if !dockerImageIDs[dbImage.ImageID] {
			// 镜像在Docker中不存在，可以选择删除或标记
			s.db.Delete(&dbImage)
		}
	}

	return nil
}

// SyncAllImages 同步所有镜像
func (s *imageService) SyncAllImages(ctx context.Context) error {
	// 获取所有主机ID
	var hostIDs []uint
	if err := s.db.Model(&entity.Image{}).Distinct("host_id").Pluck("host_id", &hostIDs).Error; err != nil {
		return fmt.Errorf("failed to get host IDs: %w", err)
	}

	// 同步每个主机的镜像
	for _, hostID := range hostIDs {
		if err := s.SyncImages(ctx, hostID); err != nil {
			// 记录错误但继续处理其他主机
			fmt.Printf("Failed to sync images for host %d: %v\n", hostID, err)
		}
	}

	return nil
}

// convertToDockerImageInfo 转换为Docker镜像信息
func (s *imageService) convertToDockerImageInfo(img *entity.Image) (*dockerTypes.ImageInfo, error) {
	info := &dockerTypes.ImageInfo{
		ID:          img.ImageID,
		RepoTags:    []string{img.Repository + ":" + img.Tag},
		Size:        img.Size,
		Created:     img.CreatedTime,
		Status:      dockerTypes.ImageStatusAvailable,
		Architecture: "amd64", // 默认值
		OS:          "linux",  // 默认值
	}

	return info, nil
}

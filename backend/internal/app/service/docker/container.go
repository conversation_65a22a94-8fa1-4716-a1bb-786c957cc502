package docker

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/go-connections/nat"
	"gorm.io/gorm"

	"panel-backend/internal/domain/entity"
	"panel-backend/internal/domain/service"
	dockerClient "panel-backend/internal/infrastructure/docker"
	dockerTypes "panel-backend/internal/infrastructure/docker"
)

// containerService 容器服务实现
type containerService struct {
	db           *gorm.DB
	dockerClient *dockerClient.Client
}

// NewContainerService 创建容器服务
func NewContainerService(db *gorm.DB, dockerClient *dockerClient.Client) service.ContainerService {
	return &containerService{
		db:           db,
		dockerClient: dockerClient,
	}
}

// List 获取容器列表
func (s *containerService) List(ctx context.Context, hostID uint, status string, search string, page, size int) ([]dockerTypes.ContainerInfo, int64, error) {
	// 构建数据库查询
	query := s.db.Model(&entity.Container{})

	if hostID > 0 {
		query = query.Where("host_id = ?", hostID)
	}

	if status != "" {
		query = query.Where("status = ?", status)
	}

	if search != "" {
		query = query.Where("name LIKE ? OR image LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count containers: %w", err)
	}

	// 分页查询
	var containers []entity.Container
	offset := (page - 1) * size
	if err := query.Preload("Host").Offset(offset).Limit(size).Find(&containers).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list containers: %w", err)
	}

	// 转换为Docker类型
	result := make([]dockerTypes.ContainerInfo, len(containers))
	for i, container := range containers {
		containerInfo, err := s.convertToDockerContainerInfo(&container)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to convert container %s: %w", container.Name, err)
		}
		result[i] = *containerInfo
	}

	return result, total, nil
}

// Get 获取容器详情
func (s *containerService) Get(ctx context.Context, id uint) (*dockerTypes.ContainerInfo, error) {
	var container entity.Container
	if err := s.db.Preload("Host").First(&container, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("container not found")
		}
		return nil, fmt.Errorf("failed to get container: %w", err)
	}

	// 从Docker API获取实时信息
	if container.ContainerID != "" {
		dockerContainer, err := s.dockerClient.ContainerInspect(ctx, container.ContainerID)
		if err == nil {
			// 更新数据库中的状态
			newStatus := s.convertDockerStatus(dockerContainer.State.Status)
			if newStatus != container.Status {
				s.db.Model(&container).Update("status", newStatus)
				container.Status = newStatus
			}
		}
	}

	return s.convertToDockerContainerInfo(&container)
}

// Create 创建容器
func (s *containerService) Create(ctx context.Context, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error) {
	// 构建Docker容器配置
	config := &container.Config{
		Image:        req.Image,
		Cmd:          req.Command,
		Entrypoint:   req.Entrypoint,
		Env:          req.Environment,
		Labels:       req.Labels,
		WorkingDir:   "",
		User:         "",
		ExposedPorts: make(nat.PortSet),
	}

	// 设置端口
	for _, port := range req.Ports {
		portKey := nat.Port(port.ContainerPort + "/" + port.Protocol)
		config.ExposedPorts[portKey] = struct{}{}
	}

	// 构建主机配置
	hostConfig := &container.HostConfig{
		RestartPolicy: container.RestartPolicy{
			Name:              container.RestartPolicyMode(req.RestartPolicy.Name),
			MaximumRetryCount: req.RestartPolicy.MaximumRetryCount,
		},
		PortBindings: make(nat.PortMap),
		Binds:        make([]string, 0),
	}

	// 设置端口映射
	for _, port := range req.Ports {
		portKey := nat.Port(port.ContainerPort + "/" + port.Protocol)
		hostConfig.PortBindings[portKey] = []nat.PortBinding{
			{
				HostIP:   port.HostIP,
				HostPort: port.HostPort,
			},
		}
	}

	// 设置卷挂载
	for _, volume := range req.Volumes {
		if volume.Type == "bind" {
			bind := volume.Source + ":" + volume.Target
			if volume.ReadOnly {
				bind += ":ro"
			}
			hostConfig.Binds = append(hostConfig.Binds, bind)
		}
	}

	// 设置资源限制
	if req.Resources.Memory > 0 {
		hostConfig.Memory = req.Resources.Memory
	}
	if req.Resources.CPUShares > 0 {
		hostConfig.CPUShares = req.Resources.CPUShares
	}

	// 构建网络配置
	networkingConfig := &network.NetworkingConfig{}

	// 创建容器
	resp, err := s.dockerClient.ContainerCreate(ctx, config, hostConfig, networkingConfig, req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to create docker container: %w", err)
	}

	// 保存到数据库
	container := entity.Container{
		ContainerID: resp.ID,
		Name:        req.Name,
		Image:       req.Image,
		Status:      string(dockerTypes.ContainerStatusCreated),
		HostID:      req.HostID,
	}

	// 序列化复杂字段
	if len(req.Ports) > 0 {
		portsJSON, _ := json.Marshal(req.Ports)
		container.Ports = string(portsJSON)
	}

	if len(req.Volumes) > 0 {
		volumesJSON, _ := json.Marshal(req.Volumes)
		container.Volumes = string(volumesJSON)
	}

	if len(req.Environment) > 0 {
		envJSON, _ := json.Marshal(req.Environment)
		container.Environment = string(envJSON)
	}

	if len(req.Command) > 0 {
		cmdJSON, _ := json.Marshal(req.Command)
		container.Command = string(cmdJSON)
	}

	if err := s.db.Create(&container).Error; err != nil {
		// 如果数据库保存失败，尝试删除已创建的Docker容器
		s.dockerClient.ContainerRemove(ctx, resp.ID, true)
		return nil, fmt.Errorf("failed to save container to database: %w", err)
	}

	return s.convertToDockerContainerInfo(&container)
}

// Update 更新容器
func (s *containerService) Update(ctx context.Context, id uint, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error) {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return nil, fmt.Errorf("container not found: %w", err)
	}

	// 更新基本信息
	container.Name = req.Name
	container.Image = req.Image

	// 序列化复杂字段
	if len(req.Ports) > 0 {
		portsJSON, _ := json.Marshal(req.Ports)
		container.Ports = string(portsJSON)
	}

	if len(req.Volumes) > 0 {
		volumesJSON, _ := json.Marshal(req.Volumes)
		container.Volumes = string(volumesJSON)
	}

	if len(req.Environment) > 0 {
		envJSON, _ := json.Marshal(req.Environment)
		container.Environment = string(envJSON)
	}

	if len(req.Command) > 0 {
		cmdJSON, _ := json.Marshal(req.Command)
		container.Command = string(cmdJSON)
	}

	if err := s.db.Save(&container).Error; err != nil {
		return nil, fmt.Errorf("failed to update container: %w", err)
	}

	return s.convertToDockerContainerInfo(&container)
}

// Delete 删除容器
func (s *containerService) Delete(ctx context.Context, id uint, force bool) error {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return fmt.Errorf("container not found: %w", err)
	}

	// 从Docker删除容器
	if container.ContainerID != "" {
		if err := s.dockerClient.ContainerRemove(ctx, container.ContainerID, force); err != nil {
			return fmt.Errorf("failed to remove docker container: %w", err)
		}
	}

	// 从数据库删除
	if err := s.db.Delete(&container).Error; err != nil {
		return fmt.Errorf("failed to delete container from database: %w", err)
	}

	return nil
}

// Start 启动容器
func (s *containerService) Start(ctx context.Context, id uint) error {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return fmt.Errorf("container not found: %w", err)
	}

	if container.ContainerID == "" {
		return fmt.Errorf("container has no docker ID")
	}

	// 启动Docker容器
	if err := s.dockerClient.ContainerStart(ctx, container.ContainerID); err != nil {
		return fmt.Errorf("failed to start docker container: %w", err)
	}

	// 更新数据库状态
	if err := s.db.Model(&container).Update("status", string(dockerTypes.ContainerStatusRunning)).Error; err != nil {
		return fmt.Errorf("failed to update container status: %w", err)
	}

	return nil
}

// Stop 停止容器
func (s *containerService) Stop(ctx context.Context, id uint, timeout *int) error {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return fmt.Errorf("container not found: %w", err)
	}

	if container.ContainerID == "" {
		return fmt.Errorf("container has no docker ID")
	}

	// 停止Docker容器
	if err := s.dockerClient.ContainerStop(ctx, container.ContainerID, timeout); err != nil {
		return fmt.Errorf("failed to stop docker container: %w", err)
	}

	// 更新数据库状态
	if err := s.db.Model(&container).Update("status", string(dockerTypes.ContainerStatusExited)).Error; err != nil {
		return fmt.Errorf("failed to update container status: %w", err)
	}

	return nil
}

// Restart 重启容器
func (s *containerService) Restart(ctx context.Context, id uint, timeout *int) error {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return fmt.Errorf("container not found: %w", err)
	}

	if container.ContainerID == "" {
		return fmt.Errorf("container has no docker ID")
	}

	// 重启Docker容器
	if err := s.dockerClient.ContainerRestart(ctx, container.ContainerID, timeout); err != nil {
		return fmt.Errorf("failed to restart docker container: %w", err)
	}

	// 更新数据库状态
	if err := s.db.Model(&container).Update("status", string(dockerTypes.ContainerStatusRunning)).Error; err != nil {
		return fmt.Errorf("failed to update container status: %w", err)
	}

	return nil
}

// GetLogs 获取容器日志
func (s *containerService) GetLogs(ctx context.Context, id uint, follow bool, tail string) ([]dockerTypes.LogEntry, error) {
	var containerEntity entity.Container
	if err := s.db.First(&containerEntity, id).Error; err != nil {
		return nil, fmt.Errorf("container not found: %w", err)
	}

	if containerEntity.ContainerID == "" {
		return nil, fmt.Errorf("container has no docker ID")
	}

	// 获取Docker容器日志
	options := container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Follow:     follow,
		Tail:       tail,
		Timestamps: true,
	}

	logs, err := s.dockerClient.ContainerLogs(ctx, containerEntity.ContainerID, options)
	if err != nil {
		return nil, fmt.Errorf("failed to get container logs: %w", err)
	}
	defer logs.Close()

	// 解析日志（这里简化处理，实际需要解析Docker日志格式）
	logEntries := []dockerTypes.LogEntry{
		{
			Timestamp: time.Now(),
			Stream:    "stdout",
			Log:       "Container logs would be parsed here",
		},
	}

	return logEntries, nil
}

// GetStats 获取容器统计信息
func (s *containerService) GetStats(ctx context.Context, id uint) (*dockerTypes.ResourceUsage, error) {
	var container entity.Container
	if err := s.db.First(&container, id).Error; err != nil {
		return nil, fmt.Errorf("container not found: %w", err)
	}

	if container.ContainerID == "" {
		return nil, fmt.Errorf("container has no docker ID")
	}

	// 获取Docker容器统计信息
	_, err := s.dockerClient.ContainerStats(ctx, container.ContainerID, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get container stats: %w", err)
	}
	// 这里简化处理，返回模拟的统计信息

	// 解析统计信息（这里简化处理，实际需要解析Docker统计格式）
	resourceUsage := &dockerTypes.ResourceUsage{
		CPUUsage: dockerTypes.CPUUsage{
			CPUPercent: 25.5,
		},
		MemoryUsage: dockerTypes.MemoryUsage{
			Usage:   128 * 1024 * 1024, // 128MB
			Limit:   512 * 1024 * 1024, // 512MB
			Percent: 25.0,
		},
		NetworkIO: dockerTypes.NetworkIO{
			RxBytes: 1024 * 100,
			TxBytes: 1024 * 50,
		},
		BlockIO: dockerTypes.BlockIO{
			ReadBytes:  1024 * 1024 * 10, // 10MB
			WriteBytes: 1024 * 1024 * 5,  // 5MB
		},
	}

	return resourceUsage, nil
}

// SyncStatus 同步容器状态
func (s *containerService) SyncStatus(ctx context.Context, hostID uint) error {
	// 获取该主机的所有容器
	var containers []entity.Container
	if err := s.db.Where("host_id = ?", hostID).Find(&containers).Error; err != nil {
		return fmt.Errorf("failed to get containers: %w", err)
	}

	// 获取Docker中的所有容器
	dockerContainers, err := s.dockerClient.ContainerList(ctx, container.ListOptions{All: true})
	if err != nil {
		return fmt.Errorf("failed to list docker containers: %w", err)
	}

	// 创建Docker容器ID到状态的映射
	dockerStatusMap := make(map[string]string)
	for _, dockerContainer := range dockerContainers {
		dockerStatusMap[dockerContainer.ID] = dockerContainer.State
	}

	// 更新数据库中的容器状态
	for _, container := range containers {
		if container.ContainerID != "" {
			if dockerStatus, exists := dockerStatusMap[container.ContainerID]; exists {
				newStatus := s.convertDockerStatus(dockerStatus)
				if newStatus != container.Status {
					s.db.Model(&container).Update("status", newStatus)
				}
			} else {
				// Docker中不存在该容器，标记为已删除
				s.db.Model(&container).Update("status", "deleted")
			}
		}
	}

	return nil
}

// SyncAllStatus 同步所有容器状态
func (s *containerService) SyncAllStatus(ctx context.Context) error {
	// 获取所有主机ID
	var hostIDs []uint
	if err := s.db.Model(&entity.Container{}).Distinct("host_id").Pluck("host_id", &hostIDs).Error; err != nil {
		return fmt.Errorf("failed to get host IDs: %w", err)
	}

	// 同步每个主机的容器状态
	for _, hostID := range hostIDs {
		if err := s.SyncStatus(ctx, hostID); err != nil {
			// 记录错误但继续处理其他主机
			fmt.Printf("Failed to sync status for host %d: %v\n", hostID, err)
		}
	}

	return nil
}

// convertToDockerContainerInfo 转换为Docker容器信息
func (s *containerService) convertToDockerContainerInfo(container *entity.Container) (*dockerTypes.ContainerInfo, error) {
	info := &dockerTypes.ContainerInfo{
		ID:      container.ContainerID,
		Name:    container.Name,
		Image:   container.Image,
		Status:  dockerTypes.ContainerStatus(container.Status),
		Created: container.CreatedAt,
	}

	// 解析端口
	if container.Ports != "" {
		var ports []dockerTypes.PortMapping
		if err := json.Unmarshal([]byte(container.Ports), &ports); err == nil {
			info.Ports = ports
		}
	}

	// 解析卷
	if container.Volumes != "" {
		var volumes []dockerTypes.VolumeMount
		if err := json.Unmarshal([]byte(container.Volumes), &volumes); err == nil {
			info.Volumes = volumes
		}
	}

	// 解析环境变量
	if container.Environment != "" {
		var env []string
		if err := json.Unmarshal([]byte(container.Environment), &env); err == nil {
			info.Environment = env
		}
	}

	// 解析命令
	if container.Command != "" {
		var cmd []string
		if err := json.Unmarshal([]byte(container.Command), &cmd); err == nil {
			info.Command = cmd
		}
	}

	return info, nil
}

// convertDockerStatus 转换Docker状态
func (s *containerService) convertDockerStatus(dockerStatus string) string {
	switch strings.ToLower(dockerStatus) {
	case "created":
		return string(dockerTypes.ContainerStatusCreated)
	case "restarting":
		return string(dockerTypes.ContainerStatusRestarting)
	case "running":
		return string(dockerTypes.ContainerStatusRunning)
	case "removing":
		return string(dockerTypes.ContainerStatusRemoving)
	case "paused":
		return string(dockerTypes.ContainerStatusPaused)
	case "exited":
		return string(dockerTypes.ContainerStatusExited)
	case "dead":
		return string(dockerTypes.ContainerStatusDead)
	default:
		return dockerStatus
	}
}

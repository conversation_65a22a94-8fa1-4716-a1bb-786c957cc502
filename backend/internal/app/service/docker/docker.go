package docker

import (
	"context"
	"fmt"
	"io"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/image"
	"github.com/docker/docker/api/types/network"
	"github.com/docker/docker/api/types/volume"
	"gorm.io/gorm"

	"panel-backend/internal/domain/service"
	dockerClient "panel-backend/internal/infrastructure/docker"
	dockerTypes "panel-backend/internal/infrastructure/docker"
)

// dockerService Docker服务主实现
type dockerService struct {
	db           *gorm.DB
	dockerClient *dockerClient.Client
}

// NewDockerService 创建Docker服务
func NewDockerService(db *gorm.DB, dockerClient *dockerClient.Client) service.DockerService {
	return &dockerService{
		db:           db,
		dockerClient: dockerClient,
	}
}

// Ping 检查Docker连接
func (s *dockerService) Ping(ctx context.Context) error {
	return s.dockerClient.Ping(ctx)
}

// GetInfo 获取Docker系统信息
func (s *dockerService) GetInfo(ctx context.Context) (interface{}, error) {
	return s.dockerClient.GetInfo(ctx)
}

// GetVersion 获取Docker版本信息
func (s *dockerService) GetVersion(ctx context.Context) (interface{}, error) {
	return s.dockerClient.GetVersion(ctx)
}

// ListContainers 列出容器
func (s *dockerService) ListContainers(ctx context.Context, all bool) ([]dockerTypes.ContainerInfo, error) {
	// 获取Docker容器列表
	containers, err := s.dockerClient.ContainerList(ctx, container.ListOptions{All: all})
	if err != nil {
		return nil, fmt.Errorf("failed to list containers: %w", err)
	}

	// 转换为内部类型
	result := make([]dockerTypes.ContainerInfo, len(containers))
	for i, c := range containers {
		result[i] = dockerTypes.ContainerInfo{
			ID:      c.ID,
			Name:    c.Names[0], // Docker返回的名称包含前缀"/"
			Image:   c.Image,
			Status:  dockerTypes.ContainerStatus(c.State),
			Created: c.Created,
		}
	}

	return result, nil
}

// GetContainer 获取容器详情
func (s *dockerService) GetContainer(ctx context.Context, containerID string) (*dockerTypes.ContainerInfo, error) {
	// 检查容器
	containerJSON, err := s.dockerClient.ContainerInspect(ctx, containerID)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect container: %w", err)
	}

	// 转换为内部类型
	info := &dockerTypes.ContainerInfo{
		ID:      containerJSON.ID,
		Name:    containerJSON.Name,
		Image:   containerJSON.Config.Image,
		ImageID: containerJSON.Image,
		Status:  dockerTypes.ContainerStatus(containerJSON.State.Status),
		State:   containerJSON.State.Status,
		Created: containerJSON.Created,
	}

	if containerJSON.State.StartedAt != "" {
		// 解析启动时间
		// 这里简化处理，实际需要解析时间字符串
	}

	return info, nil
}

// CreateContainer 创建容器
func (s *dockerService) CreateContainer(ctx context.Context, req *dockerTypes.ContainerCreateRequest) (*dockerTypes.ContainerInfo, error) {
	// 构建容器配置
	config := &container.Config{
		Image:      req.Image,
		Cmd:        req.Command,
		Entrypoint: req.Entrypoint,
		Env:        req.Environment,
		Labels:     req.Labels,
	}

	// 构建主机配置
	hostConfig := &container.HostConfig{
		RestartPolicy: container.RestartPolicy{
			Name:              container.RestartPolicyMode(req.RestartPolicy.Name),
			MaximumRetryCount: req.RestartPolicy.MaximumRetryCount,
		},
	}

	// 设置资源限制
	if req.Resources.Memory > 0 {
		hostConfig.Memory = req.Resources.Memory
	}
	if req.Resources.CPUShares > 0 {
		hostConfig.CPUShares = req.Resources.CPUShares
	}

	// 构建网络配置
	networkingConfig := &network.NetworkingConfig{}

	// 创建容器
	resp, err := s.dockerClient.ContainerCreate(ctx, config, hostConfig, networkingConfig, req.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to create container: %w", err)
	}

	// 获取创建的容器信息
	return s.GetContainer(ctx, resp.ID)
}

// StartContainer 启动容器
func (s *dockerService) StartContainer(ctx context.Context, containerID string) error {
	return s.dockerClient.ContainerStart(ctx, containerID)
}

// StopContainer 停止容器
func (s *dockerService) StopContainer(ctx context.Context, containerID string, timeout *int) error {
	return s.dockerClient.ContainerStop(ctx, containerID, timeout)
}

// RestartContainer 重启容器
func (s *dockerService) RestartContainer(ctx context.Context, containerID string, timeout *int) error {
	return s.dockerClient.ContainerRestart(ctx, containerID, timeout)
}

// RemoveContainer 删除容器
func (s *dockerService) RemoveContainer(ctx context.Context, containerID string, force bool) error {
	return s.dockerClient.ContainerRemove(ctx, containerID, force)
}

// GetContainerLogs 获取容器日志
func (s *dockerService) GetContainerLogs(ctx context.Context, containerID string, follow bool, tail string) ([]dockerTypes.LogEntry, error) {
	options := container.LogsOptions{
		ShowStdout: true,
		ShowStderr: true,
		Follow:     follow,
		Tail:       tail,
		Timestamps: true,
	}

	logs, err := s.dockerClient.ContainerLogs(ctx, containerID, options)
	if err != nil {
		return nil, fmt.Errorf("failed to get container logs: %w", err)
	}
	defer logs.Close()

	// 这里简化处理，实际需要解析Docker日志格式
	logEntries := []dockerTypes.LogEntry{
		{
			Stream: "stdout",
			Log:    "Sample log entry",
		},
	}

	return logEntries, nil
}

// GetContainerStats 获取容器统计信息
func (s *dockerService) GetContainerStats(ctx context.Context, containerID string) (*dockerTypes.ResourceUsage, error) {
	_, err := s.dockerClient.ContainerStats(ctx, containerID, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get container stats: %w", err)
	}

	// 这里简化处理，返回模拟数据
	resourceUsage := &dockerTypes.ResourceUsage{
		CPUUsage: dockerTypes.CPUUsage{
			CPUPercent: 25.5,
		},
		MemoryUsage: dockerTypes.MemoryUsage{
			Usage:   128 * 1024 * 1024,
			Limit:   512 * 1024 * 1024,
			Percent: 25.0,
		},
	}

	return resourceUsage, nil
}

// ListImages 列出镜像
func (s *dockerService) ListImages(ctx context.Context) ([]dockerTypes.ImageInfo, error) {
	images, err := s.dockerClient.ImageList(ctx, image.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list images: %w", err)
	}

	result := make([]dockerTypes.ImageInfo, len(images))
	for i, img := range images {
		result[i] = dockerTypes.ImageInfo{
			ID:       img.ID,
			RepoTags: img.RepoTags,
			Size:     img.Size,
			Status:   dockerTypes.ImageStatusAvailable,
		}
	}

	return result, nil
}

// GetImage 获取镜像详情
func (s *dockerService) GetImage(ctx context.Context, imageID string) (*dockerTypes.ImageInfo, error) {
	imageInspect, err := s.dockerClient.ImageInspect(ctx, imageID)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect image: %w", err)
	}

	info := &dockerTypes.ImageInfo{
		ID:           imageInspect.ID,
		Size:         imageInspect.Size,
		Architecture: imageInspect.Architecture,
		OS:           imageInspect.Os,
		Status:       dockerTypes.ImageStatusAvailable,
	}

	return info, nil
}

// PullImage 拉取镜像
func (s *dockerService) PullImage(ctx context.Context, imageName string, tag string) error {
	if tag == "" {
		tag = "latest"
	}

	imageRef := imageName + ":" + tag
	pullResp, err := s.dockerClient.ImagePull(ctx, imageRef, image.PullOptions{})
	if err != nil {
		return fmt.Errorf("failed to pull image: %w", err)
	}
	defer pullResp.Close()

	// 读取拉取响应
	_, err = io.ReadAll(pullResp)
	if err != nil {
		return fmt.Errorf("failed to read pull response: %w", err)
	}

	return nil
}

// RemoveImage 删除镜像
func (s *dockerService) RemoveImage(ctx context.Context, imageID string, force bool) error {
	_, err := s.dockerClient.ImageRemove(ctx, imageID, image.RemoveOptions{Force: force})
	if err != nil {
		return fmt.Errorf("failed to remove image: %w", err)
	}
	return nil
}

// ListNetworks 列出网络
func (s *dockerService) ListNetworks(ctx context.Context) ([]dockerTypes.NetworkInfo, error) {
	networks, err := s.dockerClient.NetworkList(ctx, network.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list networks: %w", err)
	}

	result := make([]dockerTypes.NetworkInfo, len(networks))
	for i, net := range networks {
		result[i] = dockerTypes.NetworkInfo{
			ID:     net.ID,
			Name:   net.Name,
			Driver: dockerTypes.NetworkDriver(net.Driver),
			Scope:  net.Scope,
		}
	}

	return result, nil
}

// GetNetwork 获取网络详情
func (s *dockerService) GetNetwork(ctx context.Context, networkID string) (*dockerTypes.NetworkInfo, error) {
	networkInspect, err := s.dockerClient.NetworkInspect(ctx, networkID, network.InspectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to inspect network: %w", err)
	}

	info := &dockerTypes.NetworkInfo{
		ID:     networkInspect.ID,
		Name:   networkInspect.Name,
		Driver: dockerTypes.NetworkDriver(networkInspect.Driver),
		Scope:  networkInspect.Scope,
	}

	return info, nil
}

// CreateNetwork 创建网络
func (s *dockerService) CreateNetwork(ctx context.Context, name string, driver dockerTypes.NetworkDriver, options map[string]string) (*dockerTypes.NetworkInfo, error) {
	createOptions := network.CreateOptions{
		Driver:  string(driver),
		Options: options,
	}

	resp, err := s.dockerClient.NetworkCreate(ctx, name, createOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create network: %w", err)
	}

	return s.GetNetwork(ctx, resp.ID)
}

// RemoveNetwork 删除网络
func (s *dockerService) RemoveNetwork(ctx context.Context, networkID string) error {
	return s.dockerClient.NetworkRemove(ctx, networkID)
}

// ListVolumes 列出卷
func (s *dockerService) ListVolumes(ctx context.Context) ([]dockerTypes.VolumeInfo, error) {
	volumes, err := s.dockerClient.VolumeList(ctx, volume.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list volumes: %w", err)
	}

	result := make([]dockerTypes.VolumeInfo, len(volumes.Volumes))
	for i, vol := range volumes.Volumes {
		result[i] = dockerTypes.VolumeInfo{
			Name:       vol.Name,
			Driver:     dockerTypes.VolumeDriver(vol.Driver),
			Mountpoint: vol.Mountpoint,
			Scope:      vol.Scope,
		}
	}

	return result, nil
}

// GetVolume 获取卷详情
func (s *dockerService) GetVolume(ctx context.Context, volumeID string) (*dockerTypes.VolumeInfo, error) {
	vol, err := s.dockerClient.VolumeInspect(ctx, volumeID)
	if err != nil {
		return nil, fmt.Errorf("failed to inspect volume: %w", err)
	}

	info := &dockerTypes.VolumeInfo{
		Name:       vol.Name,
		Driver:     dockerTypes.VolumeDriver(vol.Driver),
		Mountpoint: vol.Mountpoint,
		Scope:      vol.Scope,
	}

	return info, nil
}

// CreateVolume 创建卷
func (s *dockerService) CreateVolume(ctx context.Context, name string, driver dockerTypes.VolumeDriver, options map[string]string) (*dockerTypes.VolumeInfo, error) {
	createOptions := volume.CreateOptions{
		Name:    name,
		Driver:  string(driver),
		Options: options,
	}

	vol, err := s.dockerClient.VolumeCreate(ctx, createOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create volume: %w", err)
	}

	info := &dockerTypes.VolumeInfo{
		Name:       vol.Name,
		Driver:     dockerTypes.VolumeDriver(vol.Driver),
		Mountpoint: vol.Mountpoint,
	}

	return info, nil
}

// RemoveVolume 删除卷
func (s *dockerService) RemoveVolume(ctx context.Context, volumeID string, force bool) error {
	return s.dockerClient.VolumeRemove(ctx, volumeID, force)
}

// ListenEvents 监听Docker事件
func (s *dockerService) ListenEvents(ctx context.Context, eventChan chan<- dockerTypes.Event) error {
	// 这里简化处理，实际需要实现事件监听
	return fmt.Errorf("event listening not implemented yet")
}

import { authApi } from '@/api/auth'
import type { LoginRequest, User } from '@/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 登录
  const login = async (credentials: LoginRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      if (!response.data) {
        throw new Error('登录响应数据为空')
      }

      const { token: newToken, user: userData } = response.data

      token.value = newToken
      user.value = userData

      // 保存到本地存储
      localStorage.setItem('token', newToken)
      localStorage.setItem('user', JSON.stringify(userData))
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除状态和本地存储
      token.value = ''
      user.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  }

  // 刷新token
  const refreshToken = async (): Promise<void> => {
    try {
      const response = await authApi.refreshToken()
      if (!response.data) {
        throw new Error('刷新token响应数据为空')
      }

      const { token: newToken } = response.data

      token.value = newToken
      localStorage.setItem('token', newToken)
    } catch (error) {
      // 刷新失败，清除认证状态
      await logout()
      throw error
    }
  }

  // 获取用户信息
  const fetchProfile = async (): Promise<void> => {
    try {
      const response = await authApi.getProfile()
      if (!response.data) {
        throw new Error('获取用户信息响应数据为空')
      }

      user.value = response.data
      localStorage.setItem('user', JSON.stringify(response.data))
    } catch (error) {
      throw error
    }
  }

  // 更新用户信息
  const updateProfile = async (data: Partial<User>): Promise<void> => {
    try {
      const response = await authApi.updateProfile(data)
      if (!response.data) {
        throw new Error('更新用户信息响应数据为空')
      }

      user.value = response.data
      localStorage.setItem('user', JSON.stringify(response.data))
    } catch (error) {
      throw error
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      await authApi.changePassword({ old_password: oldPassword, new_password: newPassword })
    } catch (error) {
      throw error
    }
  }

  // 初始化用户信息（从本地存储恢复）
  const initializeAuth = (): void => {
    const savedUser = localStorage.getItem('user')
    const savedToken = localStorage.getItem('token')

    // 首先设置 token
    if (savedToken) {
      token.value = savedToken
    }

    // 然后设置 user
    if (savedUser && savedToken) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        logout()
      }
    } else {
      // 如果没有完整的认证数据，清除所有状态
      if (!savedUser || !savedToken) {
        logout()
      }
    }
  }

  return {
    // 状态
    token,
    user,
    loading,

    // 计算属性
    isAuthenticated,

    // 方法
    login,
    logout,
    refreshToken,
    fetchProfile,
    updateProfile,
    changePassword,
    initializeAuth
  }
})

import { useAuthStore } from '@/stores/auth'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/components/layout/MainLayout.vue'),
      meta: { requiresAuth: true },
      redirect: '/dashboard',
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/dashboard/Dashboard.vue'),
          meta: { title: '仪表板' }
        },
        {
          path: '/containers',
          name: 'Containers',
          component: () => import('@/views/container/ContainerList.vue'),
          meta: { title: '容器管理' }
        },
        {
          path: '/containers/:id',
          name: 'ContainerDetail',
          component: () => import('@/views/container/ContainerDetail.vue'),
          meta: { title: '容器详情' }
        },
        {
          path: '/images',
          name: 'Images',
          component: () => import('@/views/container/ImageList.vue'),
          meta: { title: '镜像管理' }
        },
        {
          path: '/networks',
          name: 'Networks',
          component: () => import('@/views/container/NetworkList.vue'),
          meta: { title: '网络管理' }
        },
        {
          path: '/volumes',
          name: 'Volumes',
          component: () => import('@/views/container/VolumeList.vue'),
          meta: { title: '卷管理' }
        },
        {
          path: '/databases',
          name: 'Databases',
          component: () => import('@/views/database/DatabaseList.vue'),
          meta: { title: '数据库管理' }
        },
        {
          path: '/websites',
          name: 'Websites',
          component: () => import('@/views/website/WebsiteList.vue'),
          meta: { title: '网站管理' }
        },
        {
          path: '/files',
          name: 'Files',
          component: () => import('@/views/file/FileManager.vue'),
          meta: { title: '文件管理' }
        },
        {
          path: '/monitor',
          name: 'Monitor',
          component: () => import('@/views/monitor/SystemMonitor.vue'),
          meta: { title: '系统监控' }
        },
        {
          path: '/settings',
          name: 'Settings',
          component: () => import('@/views/setting/SystemSettings.vue'),
          meta: { title: '系统设置' }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/NotFound.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router

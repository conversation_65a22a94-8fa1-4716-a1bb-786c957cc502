import { createPinia } from 'pinia'
import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'
import { createApp } from 'vue'

import App from './App.vue'
import router from './router'
import { showBrowserCompatibilityInfo, suppressExtensionErrors } from './utils/browserCheck'
import { createErrorHandler, filterError, logEnvironmentInfo } from './utils/errorFilter'
import { errorMonitor } from './utils/errorMonitor'

// 启用错误监控
errorMonitor

// 记录环境信息
logEnvironmentInfo()

// 显示浏览器兼容性信息
showBrowserCompatibilityInfo()

// 在开发环境中抑制扩展错误
suppressExtensionErrors()

// 全局错误处理
const handleError = createErrorHandler('Vue')

// 全局未捕获的Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  if (!filterError(event.reason, 'Promise')) {
    event.preventDefault() // 阻止扩展错误显示在控制台
  }
})

// 全局错误事件处理
window.addEventListener('error', (event) => {
  if (!filterError(event.error, 'Global')) {
    event.preventDefault() // 阻止扩展错误显示在控制台
  }
})

const app = createApp(App)

// 设置全局错误处理器
app.config.errorHandler = handleError

app.use(createPinia())

// 在路由挂载之前初始化认证状态
import { useAuthStore } from './stores/auth'
const authStore = useAuthStore()
authStore.initializeAuth()

app.use(router)
app.use(TDesign)

app.mount('#app')

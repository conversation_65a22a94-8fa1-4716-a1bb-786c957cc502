/**
 * 错误监控和报告工具
 */

interface ErrorReport {
  type: 'javascript' | 'promise' | 'resource' | 'network'
  message: string
  stack?: string
  url?: string
  line?: number
  column?: number
  timestamp: number
  userAgent: string
  isExtensionError: boolean
}

class ErrorMonitor {
  private errors: ErrorReport[] = []
  private maxErrors = 50 // 最多保存50个错误

  constructor() {
    this.init()
  }

  private init() {
    // JavaScript错误监听
    window.addEventListener('error', (event) => {
      this.recordError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        line: event.lineno,
        column: event.colno,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        isExtensionError: this.isExtensionError(event.error)
      })
    })

    // Promise错误监听
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        type: 'promise',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        isExtensionError: this.isExtensionError(event.reason)
      })
    })

    // 资源加载错误监听
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.recordError({
          type: 'resource',
          message: `Failed to load resource: ${(event.target as any)?.src || (event.target as any)?.href}`,
          url: (event.target as any)?.src || (event.target as any)?.href,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          isExtensionError: false
        })
      }
    }, true)
  }

  private isExtensionError(error: any): boolean {
    const message = error?.message || error?.toString() || ''
    const stack = error?.stack || ''
    
    const extensionPatterns = [
      'message channel closed',
      'Extension context invalidated',
      'content.js',
      'extension',
      'chrome-extension',
      'moz-extension',
      'Deprecated API for given entry type'
    ]
    
    return extensionPatterns.some(pattern => 
      message.includes(pattern) || stack.includes(pattern)
    )
  }

  private recordError(error: ErrorReport) {
    // 过滤扩展错误（在开发环境中）
    if (error.isExtensionError && import.meta.env.DEV) {
      console.warn('[ErrorMonitor] 浏览器扩展错误（已忽略）:', error.message)
      return
    }

    // 添加到错误列表
    this.errors.unshift(error)
    
    // 保持错误数量限制
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors)
    }

    // 在开发环境中记录错误
    if (import.meta.env.DEV && !error.isExtensionError) {
      console.error('[ErrorMonitor] 应用错误:', error)
    }
  }

  // 获取错误报告
  getErrors(): ErrorReport[] {
    return [...this.errors]
  }

  // 获取非扩展错误
  getAppErrors(): ErrorReport[] {
    return this.errors.filter(error => !error.isExtensionError)
  }

  // 清除错误记录
  clearErrors(): void {
    this.errors = []
  }

  // 获取错误统计
  getErrorStats() {
    const total = this.errors.length
    const extensionErrors = this.errors.filter(e => e.isExtensionError).length
    const appErrors = total - extensionErrors
    
    const errorsByType = this.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      extensionErrors,
      appErrors,
      errorsByType
    }
  }

  // 导出错误报告
  exportErrorReport(): string {
    const stats = this.getErrorStats()
    const appErrors = this.getAppErrors()
    
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      errors: appErrors.slice(0, 10) // 只导出最近10个应用错误
    }
    
    return JSON.stringify(report, null, 2)
  }
}

// 创建全局错误监控实例
export const errorMonitor = new ErrorMonitor()

// 开发环境调试工具
if (import.meta.env.DEV) {
  // 将错误监控器添加到全局对象，方便调试
  ;(window as any).__errorMonitor = errorMonitor
  
  // 添加控制台命令
  ;(window as any).__showErrors = () => {
    console.group('📊 错误统计')
    console.log(errorMonitor.getErrorStats())
    console.groupEnd()
    
    console.group('🐛 应用错误')
    console.table(errorMonitor.getAppErrors())
    console.groupEnd()
  }
  
  ;(window as any).__exportErrors = () => {
    const report = errorMonitor.exportErrorReport()
    console.log('错误报告:', report)
    
    // 创建下载链接
    const blob = new Blob([report], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `error-report-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }
  
  console.log('🔧 调试工具已加载:')
  console.log('- __showErrors() - 显示错误统计')
  console.log('- __exportErrors() - 导出错误报告')
  console.log('- __errorMonitor - 错误监控器实例')
}

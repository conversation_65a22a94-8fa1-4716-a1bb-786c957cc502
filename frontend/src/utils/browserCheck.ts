/**
 * 浏览器兼容性检查工具
 */

interface BrowserInfo {
  name: string
  version: string
  isSupported: boolean
  hasExtensions: boolean
  warnings: string[]
}

/**
 * 获取浏览器信息
 */
export function getBrowserInfo(): BrowserInfo {
  const userAgent = navigator.userAgent
  const warnings: string[] = []

  let name = 'Unknown'
  let version = 'Unknown'
  let isSupported = true

  // 检测浏览器类型和版本
  if (userAgent.includes('Chrome')) {
    name = 'Chrome'
    const match = userAgent.match(/Chrome\/(\d+)/)
    version = match ? match[1] : 'Unknown'
    isSupported = parseInt(version) >= 88 // Chrome 88+ 支持
  } else if (userAgent.includes('Firefox')) {
    name = 'Firefox'
    const match = userAgent.match(/Firefox\/(\d+)/)
    version = match ? match[1] : 'Unknown'
    isSupported = parseInt(version) >= 78 // Firefox 78+ 支持
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    name = 'Safari'
    const match = userAgent.match(/Version\/(\d+)/)
    version = match ? match[1] : 'Unknown'
    isSupported = parseInt(version) >= 14 // Safari 14+ 支持
  } else if (userAgent.includes('Edge')) {
    name = 'Edge'
    const match = userAgent.match(/Edg\/(\d+)/)
    version = match ? match[1] : 'Unknown'
    isSupported = parseInt(version) >= 88 // Edge 88+ 支持
  }

  // 检测浏览器扩展
  const hasExtensions = !!(
    (window as any).chrome?.runtime ||
    (window as any).browser?.runtime ||
    (window as any).moz ||
    document.querySelector('script[src*="extension"]')
  )

  // 生成警告信息
  if (!isSupported) {
    warnings.push(`当前浏览器版本 (${name} ${version}) 可能不完全支持此应用`)
  }

  if (hasExtensions) {
    warnings.push('检测到浏览器扩展，可能会在控制台产生一些无害的错误信息')
  }

  // 检查必要的API支持
  if (!window.fetch) {
    warnings.push('浏览器不支持 Fetch API')
    isSupported = false
  }

  if (!window.Promise) {
    warnings.push('浏览器不支持 Promise')
    isSupported = false
  }

  if (!window.localStorage) {
    warnings.push('浏览器不支持 localStorage')
    isSupported = false
  }

  return {
    name,
    version,
    isSupported,
    hasExtensions,
    warnings
  }
}

/**
 * 显示浏览器兼容性信息
 */
export function showBrowserCompatibilityInfo(): void {
  const browserInfo = getBrowserInfo()

  if (import.meta.env.DEV) {
    console.group('🌐 浏览器兼容性检查')
    console.log(`浏览器: ${browserInfo.name} ${browserInfo.version}`)
    console.log(`支持状态: ${browserInfo.isSupported ? '✅ 支持' : '❌ 不支持'}`)
    console.log(`扩展检测: ${browserInfo.hasExtensions ? '⚠️ 有扩展' : '✅ 无扩展'}`)

    if (browserInfo.warnings.length > 0) {
      console.warn('警告信息:')
      browserInfo.warnings.forEach(warning => console.warn(`- ${warning}`))
    }

    console.groupEnd()
  }

  // 在不支持的浏览器中显示警告
  if (!browserInfo.isSupported && import.meta.env.PROD) {
    const message = `您的浏览器 (${browserInfo.name} ${browserInfo.version}) 可能不完全支持此应用。建议使用最新版本的 Chrome、Firefox、Safari 或 Edge 浏览器。`

    // 创建一个简单的警告提示
    const warningDiv = document.createElement('div')
    warningDiv.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #ff9800;
      color: white;
      padding: 10px;
      text-align: center;
      z-index: 10000;
      font-family: Arial, sans-serif;
    `
    warningDiv.innerHTML = `
      <strong>浏览器兼容性警告:</strong> ${message}
      <button onclick="this.parentElement.remove()" style="margin-left: 10px; background: white; color: #ff9800; border: none; padding: 5px 10px; cursor: pointer;">关闭</button>
    `

    document.body.appendChild(warningDiv)

    // 5秒后自动关闭
    setTimeout(() => {
      if (warningDiv.parentElement) {
        warningDiv.remove()
      }
    }, 5000)
  }
}

/**
 * 检查是否在开发者工具中
 */
export function isInDevTools(): boolean {
  return !!(window as any).devtools
}

/**
 * 禁用控制台中的扩展错误（仅开发环境）
 */
export function suppressExtensionErrors(): void {
  if (import.meta.env.DEV) {
    const originalError = console.error
    const originalWarn = console.warn

    console.error = (...args: any[]) => {
      const message = args.join(' ')
      if (
        message.includes('message channel closed') ||
        message.includes('Extension context invalidated') ||
        message.includes('content.js') ||
        message.includes('Deprecated API for given entry type')
      ) {
        return // 忽略扩展相关错误
      }
      originalError.apply(console, args)
    }

    console.warn = (...args: any[]) => {
      const message = args.join(' ')
      if (
        message.includes('Extension') ||
        message.includes('content.js')
      ) {
        return // 忽略扩展相关警告
      }
      originalWarn.apply(console, args)
    }
  }
}

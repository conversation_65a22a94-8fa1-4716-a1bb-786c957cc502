/**
 * 错误过滤工具
 * 用于过滤和处理浏览器扩展相关的错误
 */

// 浏览器扩展相关的错误特征
const EXTENSION_ERROR_PATTERNS = [
  'message channel closed',
  'Extension context invalidated',
  'content.js',
  'extension',
  'chrome-extension',
  'moz-extension',
  'safari-extension',
  'A listener indicated an asynchronous response',
  'Deprecated API for given entry type'
]

// 可以忽略的错误类型
const IGNORABLE_ERROR_PATTERNS = [
  'ResizeObserver loop limit exceeded',
  'Non-Error promise rejection captured',
  'Script error',
  'Network request failed'
]

/**
 * 检查是否为浏览器扩展相关错误
 */
export function isExtensionError(error: any): boolean {
  const errorMessage = error?.message || error?.toString() || ''
  const errorStack = error?.stack || ''
  
  return EXTENSION_ERROR_PATTERNS.some(pattern => 
    errorMessage.includes(pattern) || errorStack.includes(pattern)
  )
}

/**
 * 检查是否为可忽略的错误
 */
export function isIgnorableError(error: any): boolean {
  const errorMessage = error?.message || error?.toString() || ''
  
  return IGNORABLE_ERROR_PATTERNS.some(pattern => 
    errorMessage.includes(pattern)
  )
}

/**
 * 过滤和处理错误
 */
export function filterError(error: any, context?: string): boolean {
  // 检查是否为扩展错误
  if (isExtensionError(error)) {
    console.warn(`[${context || 'Extension'}] 浏览器扩展相关错误（已忽略）:`, error.message)
    return false // 不需要进一步处理
  }
  
  // 检查是否为可忽略的错误
  if (isIgnorableError(error)) {
    console.warn(`[${context || 'Ignorable'}] 可忽略的错误:`, error.message)
    return false // 不需要进一步处理
  }
  
  return true // 需要进一步处理
}

/**
 * 安全的错误日志记录
 */
export function safeErrorLog(error: any, context?: string): void {
  if (filterError(error, context)) {
    console.error(`[${context || 'App'}] 应用错误:`, error)
  }
}

/**
 * 创建错误边界处理器
 */
export function createErrorHandler(context: string) {
  return (error: any, instance?: any, info?: string) => {
    if (filterError(error, context)) {
      console.error(`[${context}] Vue错误:`, {
        error,
        instance,
        info
      })
    }
  }
}

/**
 * 开发环境调试信息
 */
export function logEnvironmentInfo(): void {
  if (import.meta.env.DEV) {
    console.group('🔧 开发环境信息')
    console.log('Vue版本:', import.meta.env.VUE_VERSION || 'Unknown')
    console.log('Vite版本:', import.meta.env.VITE_VERSION || 'Unknown')
    console.log('Node环境:', import.meta.env.NODE_ENV)
    console.log('开发模式:', import.meta.env.DEV)
    console.log('浏览器:', navigator.userAgent)
    
    // 检查是否有扩展
    const hasExtensions = window.chrome?.runtime || window.browser?.runtime
    if (hasExtensions) {
      console.warn('⚠️ 检测到浏览器扩展，可能会产生一些无害的错误信息')
    }
    
    console.groupEnd()
  }
}

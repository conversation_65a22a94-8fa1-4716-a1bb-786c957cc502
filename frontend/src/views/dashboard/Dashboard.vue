<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <t-row :gutter="16" class="stats-row">
      <t-col :span="3">
        <t-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon cpu">
              <chart-line-icon size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview?.resource_stats.cpu_usage.toFixed(1) }}%</div>
              <div class="stat-label">CPU使用率</div>
            </div>
          </div>
        </t-card>
      </t-col>

      <t-col :span="3">
        <t-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon memory">
              <server-icon size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview?.resource_stats.memory_usage.toFixed(1) }}%</div>
              <div class="stat-label">内存使用率</div>
            </div>
          </div>
        </t-card>
      </t-col>

      <t-col :span="3">
        <t-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon disk">
              <data-base-icon size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview?.resource_stats.disk_usage.toFixed(1) }}%</div>
              <div class="stat-label">磁盘使用率</div>
            </div>
          </div>
        </t-card>
      </t-col>

      <t-col :span="3">
        <t-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon containers">
              <layers-icon size="24" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ overview?.service_stats.container_count }}</div>
              <div class="stat-label">运行容器</div>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>

    <!-- 图表区域 -->
    <t-row :gutter="16" class="charts-row">
      <t-col :span="8">
        <t-card title="系统资源监控" class="chart-card">
          <div class="chart-container">
            <div class="chart-placeholder">
              <chart-line-icon size="48" />
              <p>资源监控图表</p>
              <p class="chart-desc">实时显示CPU、内存、磁盘使用情况</p>
            </div>
          </div>
        </t-card>
      </t-col>

      <t-col :span="4">
        <t-card title="服务状态" class="chart-card">
          <div class="service-list">
            <div class="service-item">
              <div class="service-info">
                <span class="service-name">容器服务</span>
                <t-tag theme="success" size="small">运行中</t-tag>
              </div>
              <div class="service-count">{{ overview?.service_stats.container_count }} 个</div>
            </div>

            <div class="service-item">
              <div class="service-info">
                <span class="service-name">镜像管理</span>
                <t-tag theme="success" size="small">正常</t-tag>
              </div>
              <div class="service-count">{{ overview?.service_stats.image_count }} 个</div>
            </div>

            <div class="service-item">
              <div class="service-info">
                <span class="service-name">数据库</span>
                <t-tag theme="success" size="small">正常</t-tag>
              </div>
              <div class="service-count">{{ overview?.service_stats.database_count }} 个</div>
            </div>

            <div class="service-item">
              <div class="service-info">
                <span class="service-name">网站服务</span>
                <t-tag theme="success" size="small">正常</t-tag>
              </div>
              <div class="service-count">{{ overview?.service_stats.website_count }} 个</div>
            </div>
          </div>
        </t-card>
      </t-col>
    </t-row>

    <!-- 系统信息 -->
    <t-row :gutter="16" class="info-row">
      <t-col :span="6">
        <t-card title="系统信息" class="info-card">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">操作系统</span>
              <span class="info-value">{{ overview?.system_info.os }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">架构</span>
              <span class="info-value">{{ overview?.system_info.arch }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Go版本</span>
              <span class="info-value">{{ overview?.system_info.go_version }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">CPU核心</span>
              <span class="info-value">{{ overview?.system_info.num_cpu }} 核</span>
            </div>
            <div class="info-item">
              <span class="info-label">协程数</span>
              <span class="info-value">{{ overview?.system_info.num_goroutine }}</span>
            </div>
          </div>
        </t-card>
      </t-col>

      <t-col :span="6">
        <t-card title="快速操作" class="info-card">
          <div class="quick-actions">
            <t-button theme="primary" block @click="$router.push('/containers')">
              <template #icon><layers-icon /></template>
              管理容器
            </t-button>
            <t-button theme="default" block @click="$router.push('/images')">
              <template #icon><image-icon /></template>
              管理镜像
            </t-button>
            <t-button theme="default" block @click="$router.push('/databases')">
              <template #icon><data-base-icon /></template>
              管理数据库
            </t-button>
            <t-button theme="default" block @click="$router.push('/monitor')">
              <template #icon><chart-line-icon /></template>
              系统监控
            </t-button>
          </div>
        </t-card>
      </t-col>
    </t-row>
  </div>
</template>

<script setup lang="ts">
import { dashboardApi } from '@/api/dashboard'
import type { OverviewData } from '@/types'
import {
  ChartLineIcon,
  DataBaseIcon,
  ImageIcon,
  LayersIcon,
  ServerIcon
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { onMounted, ref } from 'vue'

const overview = ref<OverviewData>()
const loading = ref(false)

const fetchOverview = async () => {
  loading.value = true
  try {
    const response = await dashboardApi.getOverview()
    overview.value = response.data
  } catch (error: any) {
    MessagePlugin.error(error.message || '获取系统概览失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchOverview()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stat-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.containers {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  height: 300px;
}

.chart-container {
  height: 240px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #9ca3af;
}

.chart-placeholder p {
  margin: 8px 0 4px 0;
  font-size: 16px;
}

.chart-desc {
  font-size: 12px !important;
  color: #d1d5db !important;
}

.service-list {
  padding: 8px 0;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.service-item:last-child {
  border-bottom: none;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-name {
  font-size: 14px;
  color: #374151;
}

.service-count {
  font-size: 14px;
  color: #6b7280;
}

.info-row {
  margin-bottom: 24px;
}

.info-card {
  height: 280px;
}

.info-list {
  padding: 8px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #6b7280;
}

.info-value {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}
</style>
